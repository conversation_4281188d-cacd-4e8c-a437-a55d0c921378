import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';

interface Location {
  id: string;
  address: string;
}

interface JourneyState {
  startAddress: string;
  currentAddress: string;
  locations: Location[];
  setStartAddress: (address: string) => void;
  setCurrentAddress: (address: string) => void;
  addLocation: (location: Omit<Location, 'id'>) => void;
  removeLocation: (id: string) => void;
  reorderLocations: (locations: Location[]) => void;
  reset: () => void;
}

export const useJourneyStore = create<JourneyState>()(
  persist(
    (set) => ({
      startAddress: 'HA0 1ES',
      currentAddress: 'UB1 2UN',
      locations: [],
      setStartAddress: (address) => set({ startAddress: address }),
      setCurrentAddress: (address) => set({ currentAddress: address }),
      addLocation: (location) =>
        set((state) => ({
          locations: [...state.locations, { ...location, id: Date.now().toString() }],
          currentAddress: '',
        })),
      removeLocation: (id) =>
        set((state) => ({
          locations: state.locations.filter((loc) => loc.id !== id),
        })),
      reorderLocations: (locations) => set({ locations }),
      reset: () =>
        set({
          startAddress: '',
          currentAddress: '',
          locations: [],
        }),
    }),
    {
      name: 'journey-storage',
      storage: createJSONStorage(() => localStorage),
    }
  )
);

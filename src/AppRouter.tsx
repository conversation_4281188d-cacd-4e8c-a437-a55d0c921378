import { BrowserRouter as Router, Routes, Route, Navigate, Outlet } from 'react-router-dom';
import { QueryClientProvider } from '@tanstack/react-query';
import { AuthProvider, useAuth } from './contexts/AuthContext';
import GoogleMapsProvider from './components/GoogleMapsProvider';
import LandingPage from './pages/LandingPage';
import App from './App';
import { queryClient } from './config/queryClient';
import TrialBanner from './components/TrialBanner';
import { LoginPage } from './pages/auth/LoginPage';
import { SignupPage } from './pages/auth/SignupPage';
import { ForgotPasswordPage } from './pages/auth/ForgotPasswordPage';
import { ResetPasswordPage } from './pages/auth/ResetPasswordPage';
import { DashboardPage } from './pages/DashboardPage';
import { JourneyHistory } from './components/journey/JourneyHistory';
import { JourneyDetail } from './components/journey/JourneyDetail';
import CreateJourneyPage from './pages/CreateJourneyPage';
import EditJourneyPage from './pages/EditJourneyPage';

// A wrapper for routes that require authentication
const ProtectedRoute = () => {
  const { user, loading, isPhoneVerified } = useAuth();
  
  // Show loading state while checking auth status
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }
  
  // Redirect to login if not authenticated
  if (!user) {
    return <Navigate to="/login" replace state={{ from: location.pathname }} />;
  }
  
  // Redirect to phone verification if phone is not verified
  if (user.phone && !isPhoneVerified) {
    return <Navigate to="/verify-phone" state={{ from: location.pathname }} />;
  }
  
  // Render the protected route
  return <Outlet />;
};

// A wrapper for auth routes that should only be accessible to unauthenticated users
const AuthRoute = () => {
  const { user, loading } = useAuth();
  
  // Show loading state while checking auth status
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }
  
  // Redirect to app if already authenticated
  if (user) {
    return <Navigate to="/app" replace />;
  }
  
  // Render the auth route
  return <Outlet />;
};

export const AppRouter = () => {
  return (
    <QueryClientProvider client={queryClient}>
      <AuthProvider>
        <GoogleMapsProvider>
          <Router>
            <TrialBanner />
            <Routes>
              {/* Public routes */}
              <Route path="/" element={<LandingPage />} />
              
              {/* Auth routes - only accessible when not logged in */}
              <Route element={<AuthRoute />}>
                <Route path="/login" element={<LoginPage />} />
                <Route path="/signup" element={<SignupPage />} />
                <Route path="/forgot-password" element={<ForgotPasswordPage />} />
                <Route path="/reset-password" element={<ResetPasswordPage />} />
              </Route>
              
              {/* Protected routes - only accessible when logged in */}
              <Route element={<ProtectedRoute />}>
                <Route path="/app" element={<App />}>
                  <Route index element={<DashboardPage />} />
                  <Route path="dashboard" element={<DashboardPage />} />
                  
                  {/* Journey routes */}
                  <Route path="journeys">
                    <Route index element={<JourneyHistory />} />
                    <Route path="new" element={<CreateJourneyPage />} />
                    <Route path=":id">
                      <Route index element={<JourneyDetail />} />
                      <Route path="edit" element={<EditJourneyPage />} />
                    </Route>
                  </Route>
                  
                  {/* Add more protected routes here */}
                </Route>
              </Route>
              
              {/* Catch all - redirect to home */}
              <Route path="*" element={<Navigate to="/" replace />} />
            </Routes>
          </Router>
        </GoogleMapsProvider>
      </AuthProvider>
    </QueryClientProvider>
  );
};

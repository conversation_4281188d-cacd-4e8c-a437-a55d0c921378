export interface Waypoint {
  id: string;
  address: string;
  lat: number | null;
  lng: number | null;
  order: number;
  completed: boolean;
  notes?: string;
}

export interface Journey {
  id: string;
  userId: string;
  name: string;
  startAddress: string;
  endAddress: string;
  waypoints: Waypoint[];
  totalDistance: number; // in meters
  totalDuration: number; // in seconds
  startedAt: string;
  completedAt: string | null;
  createdAt: string;
  updatedAt: string;
}

export interface JourneySummary {
  id: string;
  name: string;
  startAddress: string;
  endAddress: string;
  totalStops: number;
  totalDistance: number; // in meters
  totalDuration: number; // in seconds
  startedAt: string;
  completedAt: string | null;
}

export interface CreateWaypointInput {
  address: string;
  lat?: number | null;
  lng?: number | null;
  order: number;
  notes?: string;
}

export interface CreateJourneyInput {
  name: string;
  startAddress: string;
  endAddress: string;
  waypoints: CreateWaypointInput[];
}

export interface UpdateJourneyInput extends Partial<CreateJourneyInput> {
  completed?: boolean;
}

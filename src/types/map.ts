import { LatLngLiteral } from 'leaflet';

export interface RouteWaypoint {
  location: LatLngLiteral;
  stopover: boolean;
  address: string;
  order: number;
}

export interface RouteLeg {
  distance: {
    text: string;
    value: number;
  };
  duration: {
    text: string;
    value: number;
  };
  end_address: string;
  end_location: google.maps.LatLngLiteral;
  start_address: string;
  start_location: google.maps.LatLngLiteral;
  steps: any[];
  traffic_speed_entry: any[];
  via_waypoint: any[];
}

export interface Route {
  bounds: google.maps.LatLngBounds;
  legs: RouteLeg[];
  overview_path: google.maps.LatLngLiteral[];
  overview_polyline: string;
  summary: string;
  warnings: string[];
  waypoint_order: number[];
}

export interface MapRoute {
  origin: LatLngLiteral;
  destination: LatLngLiteral;
  waypoints: RouteWaypoint[];
  route?: Route;
}

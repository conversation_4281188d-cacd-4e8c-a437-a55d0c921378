import React from 'react';
import { LoadScript } from '@react-google-maps/api';
// import { GOOGLE_MAPS_API_KEY } from '../config/maps';

interface GoogleMapsProviderProps {
  children: React.ReactNode;
}

const GOOGLE_MAPS_API_KEY = import.meta.env.VITE_GOOGLE_MAPS_API_KEY;
const libraries: ("places")[] = ["places"];

const GoogleMapsProvider: React.FC<GoogleMapsProviderProps> = ({ children }) => {
  return (
    <LoadScript
      googleMapsApiKey={GOOGLE_MAPS_API_KEY}
      libraries={libraries}
    >
      {children}
    </LoadScript>
  );
};

export default GoogleMapsProvider;
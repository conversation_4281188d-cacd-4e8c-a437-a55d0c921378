import { useEffect, useState } from 'react';
import { X, AlertTriangle } from 'lucide-react';
import { isTrialActive, getDaysRemaining, startTrial } from '../services/trialService';

export default function TrialBanner() {
  const [showBanner, setShowBanner] = useState(true);
  const [trialActive, setTrialActive] = useState(false);
  const [daysRemaining, setDaysRemaining] = useState<number | null>(null);

  useEffect(() => {
    const trialActive = isTrialActive();
    setTrialActive(trialActive);
    
    if (trialActive) {
      setDaysRemaining(getDaysRemaining());
    } else if (!localStorage.getItem('trial_dismissed')) {
      // Auto-start trial for new users
      const trialData = startTrial();
      setTrialActive(true);
      setDaysRemaining(getDaysRemaining());
    } else {
      setShowBanner(false);
    }
  }, []);

  const handleDismiss = () => {
    setShowBanner(false);
    localStorage.setItem('trial_dismissed', 'true');
  };

  if (!showBanner) return null;

  return (
    <div className="bg-gradient-to-r from-blue-600 to-indigo-600 text-white p-4 relative">
      <div className="container mx-auto px-4 flex items-center justify-between">
        <div className="flex items-center">
          <AlertTriangle className="w-5 h-5 mr-3 flex-shrink-0" />
          <div>
            {trialActive ? (
              daysRemaining !== null && daysRemaining > 0 ? (
                <p className="text-sm sm:text-base">
                  <span className="font-semibold">{daysRemaining} days</span> left in your free trial. No credit card required.
                </p>
              ) : (
                <p className="text-sm sm:text-base">
                  Your free trial has ended. <a href="/pricing" className="underline font-semibold hover:opacity-90">Upgrade now</a> to continue using all features.
                </p>
              )
            ) : (
              <p className="text-sm sm:text-base">
                Start your <span className="font-semibold">14-day free trial</span>. No credit card required.
              </p>
            )}
          </div>
        </div>
        
        <button
          onClick={handleDismiss}
          className="ml-4 p-1 rounded-full hover:bg-white/20 transition-colors"
          aria-label="Dismiss trial banner"
        >
          <X className="w-4 h-4" />
        </button>
      </div>
    </div>
  );
}

import React, { useCallback, useEffect, useRef, useState } from 'react';
import { GoogleMap, LoadScript, DirectionsService, DirectionsRenderer, <PERSON><PERSON>, InfoWindow } from '@react-google-maps/api';
import { Button } from '@/components/ui/button';
import { Maximize2, Minimize2, ZoomIn, ZoomOut, Compass, Navigation, Loader2, AlertCircle } from 'lucide-react';

// Define our types
type MapWaypoint = {
  id: string;
  address: string;
  lat: number | null;
  lng: number | null;
  order: number;
  completed: boolean;
  notes?: string;
  isStart?: boolean;
  isEnd?: boolean;
};

type Journey = {
  id: string;
  name: string;
  startAddress: string;
  endAddress: string;
  waypoints: MapWaypoint[];
  totalDistance: number;
  totalDuration: number;
  startedAt: string;
  completedAt: string | null;
  createdAt: string;
  updatedAt: string;
};

type MapViewProps = {
  journey: Journey;
  onRouteCalculated?: (route: google.maps.DirectionsResult) => void;
  onJourneyStart?: () => void;
  isJourneyStarted?: boolean;
  className?: string;
};

// Map container styles
const mapContainerStyle: React.CSSProperties = {
  width: '100%',
  height: '100%',
  minHeight: '400px',
  borderRadius: '0.5rem',
  position: 'relative',
  overflow: 'hidden'
};

// Default center (London)
const defaultCenter = {
  lat: 51.5074,
  lng: -0.1278
};

export const MapView: React.FC<MapViewProps> = ({
  journey,
  onRouteCalculated,
  onJourneyStart,
  isJourneyStarted = false,
  className = ''
}) => {
  const [route, setRoute] = useState<google.maps.DirectionsResult | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeWaypoint, setActiveWaypoint] = useState<number | null>(null);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [map, setMap] = useState<google.maps.Map | null>(null);
  const mapRef = useRef<google.maps.Map | null>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const directionsService = useRef<google.maps.DirectionsService | null>(null);

  // Toggle fullscreen mode
  const toggleFullscreen = useCallback(() => {
    if (!containerRef.current) return;
    
    if (!document.fullscreenElement) {
      containerRef.current.requestFullscreen().catch(err => {
        console.error(`Error attempting to enable fullscreen: ${err.message}`);
      });
      setIsFullscreen(true);
    } else {
      document.exitFullscreen();
      setIsFullscreen(false);
    }
  }, []);

  // Calculate route when journey changes
  const calculateRoute = useCallback(() => {
    if (!journey) return;

    setLoading(true);
    setError(null);

    try {
      // Filter out waypoints that don't have coordinates
      const validWaypoints = journey.waypoints
        .filter((wp): wp is MapWaypoint & { lat: number; lng: number } => 
          wp.lat !== null && wp.lng !== null
        )
        .map(wp => ({
          location: { lat: wp.lat, lng: wp.lng },
          stopover: true,
        }));

      if (!directionsService.current) {
        directionsService.current = new window.google.maps.DirectionsService();
      }

      const request: google.maps.DirectionsRequest = {
        destination: journey.endAddress,
        origin: journey.startAddress,
        travelMode: google.maps.TravelMode.DRIVING,
        waypoints: validWaypoints,
        optimizeWaypoints: true,
      };

      directionsService.current.route(request, (result, status) => {
        if (status === 'OK' && result) {
          setRoute(result);
          onRouteCalculated?.(result);
          setLoading(false);
        } else {
          setError('Failed to calculate route. Please try again.');
          setLoading(false);
        }
      });
    } catch (err) {
      console.error('Error calculating route:', err);
      setError('Failed to calculate route. Please try again.');
      setLoading(false);
    }
  }, [journey, onRouteCalculated]);

  // Map control handlers
  const centerMap = useCallback((position: google.maps.LatLngLiteral) => {
    if (mapRef.current) {
      mapRef.current.panTo(position);
      mapRef.current.setZoom(15);
    }
  }, []);

  const handleZoomIn = useCallback(() => {
    if (map) {
      const currentZoom = map.getZoom() || 12;
      map.setZoom(currentZoom + 1);
    }
  }, [map]);

  const handleZoomOut = useCallback(() => {
    if (map) {
      const currentZoom = map.getZoom() || 12;
      map.setZoom(currentZoom - 1);
    }
  }, [map]);

  const handleCenterMap = useCallback(() => {
    if (map && journey.waypoints.length > 0) {
      const bounds = new window.google.maps.LatLngBounds();
      journey.waypoints.forEach(wp => {
        if (wp.lat && wp.lng) {
          bounds.extend({ lat: wp.lat, lng: wp.lng });
        }
      });
      map.fitBounds(bounds);
    }
  }, [map, journey.waypoints]);

  // Calculate route when component mounts or journey changes
  useEffect(() => {
    calculateRoute();
  }, [calculateRoute]);

  // Render waypoint markers
  const renderWaypoints = useCallback(() => {
    if (!journey?.waypoints?.length) return null;

    return journey.waypoints.map((waypoint, index) => {
      if (!waypoint.lat || !waypoint.lng) return null;

      const position = { lat: waypoint.lat, lng: waypoint.lng };
      const isActive = activeWaypoint === index;

      return (
        <Marker
          key={`${waypoint.id}-${index}`}
          position={position}
          onClick={() => setActiveWaypoint(index)}
          icon={{
            url: waypoint.isStart 
              ? 'https://maps.google.com/mapfiles/ms/icons/green-dot.png'
              : waypoint.isEnd
                ? 'https://maps.google.com/mapfiles/ms/icons/red-dot.png'
                : 'https://maps.google.com/mapfiles/ms/icons/blue-dot.png',
            scaledSize: new window.google.maps.Size(32, 32),
          }}
        >
          {isActive && (
            <InfoWindow
              onCloseClick={() => setActiveWaypoint(null)}
              position={position}
            >
              <div className="p-2">
                <h4 className="font-medium">
                  {waypoint.isStart 
                    ? 'Start' 
                    : waypoint.isEnd 
                      ? 'Destination' 
                      : `Stop ${waypoint.order}`}
                </h4>
                <p className="text-sm text-gray-600">{waypoint.address}</p>
                {waypoint.notes && (
                  <p className="mt-1 text-sm text-gray-500">{waypoint.notes}</p>
                )}
              </div>
            </InfoWindow>
          )}
        </Marker>
      );
    });
  }, [journey, activeWaypoint]);

  // Render map controls
  const renderMapControls = useCallback(() => (
    <div className="absolute right-4 top-4 z-10 flex flex-col space-y-2">
      <Button
        variant="outline"
        size="icon"
        onClick={handleZoomIn}
        className="bg-background/80 backdrop-blur-sm"
      >
        <ZoomIn className="h-4 w-4" />
      </Button>
      <Button
        variant="outline"
        size="icon"
        onClick={handleZoomOut}
        className="bg-background/80 backdrop-blur-sm"
      >
        <ZoomOut className="h-4 w-4" />
      </Button>
      <Button
        variant="outline"
        size="icon"
        onClick={handleCenterMap}
        className="bg-background/80 backdrop-blur-sm"
      >
        <Compass className="h-4 w-4" />
      </Button>
      <Button
        variant="outline"
        size="icon"
        onClick={toggleFullscreen}
        className="bg-background/80 backdrop-blur-sm"
      >
        {isFullscreen ? (
          <Minimize2 className="h-4 w-4" />
        ) : (
          <Maximize2 className="h-4 w-4" />
        )}
      </Button>
      {onJourneyStart && (
        <Button
          variant={isJourneyStarted ? "default" : "secondary"}
          size="sm"
          onClick={onJourneyStart}
          className="mt-2 whitespace-nowrap"
          disabled={isJourneyStarted}
        >
          {isJourneyStarted ? (
            <>
              <Navigation className="mr-2 h-4 w-4" />
              Journey Started
            </>
          ) : (
            <>
              <Navigation className="mr-2 h-4 w-4" />
              Start Journey
            </>
          )}
        </Button>
      )}
    </div>
  ), [handleZoomIn, handleZoomOut, handleCenterMap, toggleFullscreen, isFullscreen, onJourneyStart, isJourneyStarted]);

  // Render loading state
  if (loading) {
    return (
      <div className="flex h-full items-center justify-center">
        <Loader2 className="mr-2 h-8 w-8 animate-spin text-primary" />
        <span>Loading map...</span>
      </div>
    );
  }

  // Render error state
  if (error) {
    return (
      <div className="flex h-full flex-col items-center justify-center p-4 text-center">
        <AlertCircle className="mb-2 h-8 w-8 text-destructive" />
        <p className="font-medium text-destructive">{error}</p>
        <Button
          variant="outline"
          className="mt-4"
          onClick={calculateRoute}
        >
          Retry
        </Button>
      </div>
    );
  }

  // Main map render
  return (
    <div 
      className={`relative h-full w-full overflow-hidden rounded-lg border bg-background ${className} ${
        isFullscreen ? 'fixed inset-0 z-50' : ''
      }`}
      ref={containerRef}
    >
      <LoadScript googleMapsApiKey={import.meta.env.VITE_GOOGLE_MAPS_API_KEY}>
        <GoogleMap
          mapContainerStyle={mapContainerStyle}
          center={defaultCenter}
          zoom={12}
          onLoad={map => {
            mapRef.current = map;
            setMap(map);
            // Fit bounds when route is available
            if (journey?.waypoints?.length) {
              const bounds = new window.google.maps.LatLngBounds();
              journey.waypoints.forEach(wp => {
                if (wp.lat && wp.lng) {
                  bounds.extend({ lat: wp.lat, lng: wp.lng });
                }
              });
              map.fitBounds(bounds);
            }
          }}
          options={{
            mapTypeControl: false,
            streetViewControl: false,
            fullscreenControl: false,
            zoomControl: false,
            styles: [
              {
                featureType: 'poi',
                elementType: 'labels',
                stylers: [{ visibility: 'off' }],
              },
            ],
          }}
        >
          {renderWaypoints()}
          {route && <DirectionsRenderer directions={route} />}
        </GoogleMap>
      </LoadScript>
      
      {renderMapControls()}
    </div>
  );
};

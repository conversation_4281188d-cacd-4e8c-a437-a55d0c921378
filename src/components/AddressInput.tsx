import React, { useEffect, useRef } from 'react';
import { Loader } from '@googlemaps/js-api-loader';
import { MapPin } from 'lucide-react';

interface AddressInputProps {
  placeholder: string;
  onSelect: (address: string) => void;
  value: string;
  apiKey: string;
}

const AddressInput: React.FC<AddressInputProps> = ({ placeholder, onSelect, value, apiKey }) => {
  const inputRef = useRef<HTMLInputElement>(null);
  const autocompleteRef = useRef<google.maps.places.Autocomplete | null>(null);

  useEffect(() => {
    // Load the Google Maps API and initialize the autocomplete
    const loader = new Loader({
      apiKey: apiKey,
      version: 'weekly',
      libraries: ['places']
    });

    const initializeAutocomplete = async () => {
      try {
        await loader.load();
        
        if (!inputRef.current) return;
        
        // Create the autocomplete
        autocompleteRef.current = new google.maps.places.Autocomplete(
          inputRef.current,
          {
            fields: ['formatted_address', 'geometry', 'name'],
            types: ['address'],
            componentRestrictions: { country: 'us' } // Optional: restrict to US addresses
          }
        );

        // Listen for place changes
        autocompleteRef.current.addListener('place_changed', () => {
          const place = autocompleteRef.current?.getPlace();
          if (place?.formatted_address) {
            onSelect(place.formatted_address);
          }
        });
      } catch (error) {
        console.error('Error initializing Google Maps Autocomplete:', error);
      }
    };

    initializeAutocomplete();

    // Cleanup
    return () => {
      if (autocompleteRef.current) {
        google.maps.event.clearInstanceListeners(autocompleteRef.current);
      }
    };
  }, [apiKey, onSelect]);

  return (
    <div className="relative w-full">
      <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">
        <MapPin size={20} />
      </div>
      <input
        ref={inputRef}
        type="text"
        className="w-full pl-10 pr-4 py-3 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        placeholder={placeholder}
        value={value}
        onChange={(e) => onSelect(e.target.value)}
      />
    </div>
  );
};

export default AddressInput;
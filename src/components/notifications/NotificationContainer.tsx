import { useEffect, useState } from 'react';
import { AnimatePresence, motion } from 'framer-motion';
import { useNotifications } from '@/contexts/NotificationContext';
import { Notification as NotificationComponent } from './Notification';

export const NotificationContainer = () => {
  const { notifications, removeNotification } = useNotifications();
  const [mounted, setMounted] = useState(false);

  // Prevent hydration mismatch
  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) return null;

  return (
    <div className="fixed top-4 right-4 z-50 flex w-full max-w-xs flex-col space-y-4 sm:right-4 sm:top-4">
      <AnimatePresence initial={false}>
        {notifications.map((notification) => (
          <motion.div
            key={notification.id}
            layout
            initial={{ opacity: 0, y: -50, scale: 0.9 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, x: '100%', scale: 0.9 }}
            transition={{
              type: 'spring',
              damping: 25,
              stiffness: 300,
              mass: 0.5,
            }}
            className="w-full"
          >
            <NotificationComponent
              {...notification}
              onDismiss={() => removeNotification(notification.id)}
              className="shadow-lg"
            />
          </motion.div>
        ))}
      </AnimatePresence>
    </div>
  );
};

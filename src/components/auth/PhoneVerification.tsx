import { useState, useEffect } from 'react';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { useToast } from '../ui/use-toast';
import { useAuth } from '../../contexts/AuthContext';

interface PhoneVerificationProps {
  phone: string;
  onVerificationComplete: () => void;
}

export const PhoneVerification = ({ phone, onVerificationComplete }: PhoneVerificationProps) => {
  const [code, setCode] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [resendTimer, setResendTimer] = useState(60);
  const [canResend, setCanResend] = useState(false);
  const { verifyPhone, sendPhoneVerification } = useAuth();
  const { toast } = useToast();

  // Handle resend timer
  useEffect(() => {
    let timer: NodeJS.Timeout;
    if (resendTimer > 0) {
      timer = setTimeout(() => setResendTimer(resendTimer - 1), 1000);
    } else {
      setCanResend(true);
    }
    return () => clearTimeout(timer);
  }, [resendTimer]);

  const handleVerify = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!code) return;

    setIsLoading(true);
    try {
      const { error, verified } = await verifyPhone(code);
      if (error) throw error;
      
      if (verified) {
        toast({
          title: 'Success',
          description: 'Your phone number has been verified!',
          variant: 'default',
        });
        onVerificationComplete();
      }
    } catch (error) {
      console.error('Verification error:', error);
      toast({
        title: 'Error',
        description: 'Invalid verification code. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleResendCode = async () => {
    if (!canResend) return;

    setIsLoading(true);
    try {
      const { error } = await sendPhoneVerification(phone);
      if (error) throw error;
      
      setResendTimer(60);
      setCanResend(false);
      toast({
        title: 'Code resent',
        description: 'A new verification code has been sent to your phone.',
        variant: 'default',
      });
    } catch (error) {
      console.error('Resend error:', error);
      toast({
        title: 'Error',
        description: 'Failed to resend verification code. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h3 className="text-lg font-medium">Verify Your Phone Number</h3>
        <p className="text-sm text-muted-foreground">
          We've sent a verification code to {phone}
        </p>
      </div>

      <form onSubmit={handleVerify} className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="code">Verification Code</Label>
          <Input
            id="code"
            type="text"
            value={code}
            onChange={(e) => setCode(e.target.value)}
            placeholder="Enter 6-digit code"
            maxLength={6}
            disabled={isLoading}
            className="text-center text-lg font-mono tracking-widest"
          />
        </div>

        <Button
          type="submit"
          className="w-full"
          disabled={isLoading || code.length !== 6}
        >
          {isLoading ? 'Verifying...' : 'Verify Phone Number'}
        </Button>

        <div className="text-center text-sm text-muted-foreground">
          <p>
            Didn't receive a code?{' '}
            <button
              type="button"
              onClick={handleResendCode}
              disabled={!canResend || isLoading}
              className="text-primary hover:underline focus:outline-none disabled:opacity-50"
            >
              {canResend ? 'Resend Code' : `Resend in ${resendTimer}s`}
            </button>
          </p>
        </div>
      </form>
    </div>
  );
};

export default PhoneVerification;

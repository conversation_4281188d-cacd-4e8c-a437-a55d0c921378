import React from 'react';
import { DragDropContext, Droppable, Draggable } from 'react-beautiful-dnd';
import { GripVertical, X } from 'lucide-react';

interface Location {
  id: string;
  address: string;
}

interface LocationListProps {
  locations: Location[];
  onReorder: (locations: Location[]) => void;
  onRemove: (id: string) => void;
}

const LocationList: React.FC<LocationListProps> = ({ locations, onReorder, onRemove }) => {
  const handleDragEnd = (result: any) => {
    if (!result.destination) return;
    
    const items = Array.from(locations);
    const [reorderedItem] = items.splice(result.source.index, 1);
    items.splice(result.destination.index, 0, reorderedItem);
    
    onReorder(items);
  };

  return (
    <DragDropContext onDragEnd={handleDragEnd}>
      <Droppable droppableId="locations">
        {(provided) => (
          <div
            {...provided.droppableProps}
            ref={provided.innerRef}
            className="space-y-2"
          >
            {locations.map((location, index) => (
              <Draggable key={location.id} draggableId={location.id} index={index}>
                {(provided) => (
                  <div
                    ref={provided.innerRef}
                    {...provided.draggableProps}
                    className="flex items-center bg-white p-3 rounded-lg shadow-sm border border-gray-100"
                  >
                    <div {...provided.dragHandleProps} className="mr-3 text-gray-400">
                      <GripVertical size={20} />
                    </div>
                    <span className="flex-1 text-gray-700">{location.address}</span>
                    <button
                      onClick={() => onRemove(location.id)}
                      className="ml-2 text-gray-400 hover:text-red-500 transition-colors"
                    >
                      <X size={20} />
                    </button>
                  </div>
                )}
              </Draggable>
            ))}
            {provided.placeholder}
          </div>
        )}
      </Droppable>
    </DragDropContext>
  );
};

export default LocationList;
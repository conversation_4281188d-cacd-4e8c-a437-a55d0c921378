import { useState, useEffect } from 'react';
import { use<PERSON><PERSON>, SubmitHandler } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useNavigate } from 'react-router-dom';
import { Plus, MapPin, X, ChevronUp, ChevronDown, Loader2 } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/components/ui/use-toast';
import { useNotification } from '@/hooks/useNotification';
import { Journey, CreateJourneyInput, UpdateJourneyInput } from '@/types/journey';

const journeyFormSchema = z.object({
  name: z.string().min(1, 'Name is required').max(100, 'Name is too long'),
  startAddress: z.string().min(1, 'Start address is required'),
  endAddress: z.string().min(1, 'End address is required'),
  waypoints: z.array(
    z.object({
      id: z.string(),
      address: z.string().min(1, 'Address is required'),
      order: z.number(),
    })
  ).optional(),
  notes: z.string().optional(),
});

type JourneyFormValues = z.infer<typeof journeyFormSchema>;

interface JourneyFormProps {
  journey?: Journey;
  onSubmit: (data: CreateJourneyInput | UpdateJourneyInput) => Promise<void>;
  isSubmitting?: boolean;
}

export const JourneyForm = ({ journey, onSubmit, isSubmitting = false }: JourneyFormProps) => {
  const { toast } = useToast();
  const { success, error } = useNotification();
  const navigate = useNavigate();
  
  const [isLoading, setIsLoading] = useState(false);
  const [waypoints, setWaypoints] = useState<Array<{ id: string; address: string; order: number }>>(
    journey?.waypoints.map(wp => ({
      id: wp.id,
      address: wp.address,
      order: wp.order,
    })) || []
  );

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
  } = useForm<JourneyFormValues>({
    resolver: zodResolver(journeyFormSchema),
    defaultValues: {
      name: journey?.name || '',
      startAddress: journey?.startAddress || '',
      endAddress: journey?.endAddress || '',
      notes: '',
    },
  });

  const watchedStartAddress = watch('startAddress');
  const watchedEndAddress = watch('endAddress');

  useEffect(() => {
    const waypointAddresses = waypoints.map(wp => wp.address);
    const allAddresses = [
      watchedStartAddress,
      ...waypointAddresses,
      watchedEndAddress,
    ].filter(Boolean);
    
    // Here you would typically call a geocoding service to get coordinates
    // and calculate the optimal route
    console.log('All addresses for route optimization:', allAddresses);
  }, [watchedStartAddress, watchedEndAddress, waypoints]);

  const addWaypoint = () => {
    const newWaypoint = {
      id: `wp-${Date.now()}`,
      address: '',
      order: waypoints.length,
    };
    setWaypoints([...waypoints, newWaypoint]);
  };

  const removeWaypoint = (index: number) => {
    const updatedWaypoints = waypoints.filter((_, i) => i !== index);
    // Update order numbers
    const reorderedWaypoints = updatedWaypoints.map((wp, i) => ({
      ...wp,
      order: i,
    }));
    setWaypoints(reorderedWaypoints);
  };

  const moveWaypoint = (index: number, direction: 'up' | 'down') => {
    if (
      (direction === 'up' && index === 0) ||
      (direction === 'down' && index === waypoints.length - 1)
    ) {
      return;
    }

    const newWaypoints = [...waypoints];
    const newIndex = direction === 'up' ? index - 1 : index + 1;
    
    // Swap the waypoints
    [newWaypoints[index], newWaypoints[newIndex]] = [
      newWaypoints[newIndex],
      newWaypoints[index],
    ];

    // Update order numbers
    const reorderedWaypoints = newWaypoints.map((wp, i) => ({
      ...wp,
      order: i,
    }));

    setWaypoints(reorderedWaypoints);
  };

  const handleAddressChange = (index: number, value: string) => {
    const updatedWaypoints = [...waypoints];
    updatedWaypoints[index] = {
      ...updatedWaypoints[index],
      address: value,
    };
    setWaypoints(updatedWaypoints);
  };

  const processForm: SubmitHandler<JourneyFormValues> = async (data) => {
    try {
      setIsLoading(true);
      
      const journeyData = {
        ...data,
        waypoints: waypoints.map((wp, index) => ({
          address: wp.address,
          order: index,
        })),
      };

      await onSubmit(journeyData);
      
      success(
        journey ? 'Journey updated' : 'Journey created',
        `Your journey "${data.name}" has been ${journey ? 'updated' : 'created'} successfully.`
      );
      
      navigate('/app/journeys');
    } catch (err) {
      console.error('Error saving journey:', err);
      error(
        'Error',
        `Failed to ${journey ? 'update' : 'create'} journey. Please try again.`
      );
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit(processForm)} className="space-y-6">
      <div className="space-y-4">
        <div>
          <Label htmlFor="name">Journey Name</Label>
          <Input
            id="name"
            placeholder="e.g., Weekend Road Trip"
            {...register('name')}
            disabled={isLoading || isSubmitting}
          />
          {errors.name && (
            <p className="text-sm text-red-500 mt-1">{errors.name.message}</p>
          )}
        </div>

        <div className="space-y-4">
          <div>
            <Label htmlFor="startAddress">Start Location</Label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                <MapPin className="h-4 w-4 text-muted-foreground" />
              </div>
              <Input
                id="startAddress"
                className="pl-10"
                placeholder="Starting point"
                {...register('startAddress')}
                disabled={isLoading || isSubmitting}
              />
            </div>
            {errors.startAddress && (
              <p className="text-sm text-red-500 mt-1">{errors.startAddress.message}</p>
            )}
          </div>

          {waypoints.map((waypoint, index) => (
            <div key={waypoint.id} className="relative group">
              <div className="absolute left-3 top-3.5 -ml-1 text-muted-foreground text-sm">
                {index + 1}.
              </div>
              <div className="absolute right-2 top-2 flex space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
                <button
                  type="button"
                  onClick={() => moveWaypoint(index, 'up')}
                  disabled={index === 0 || isLoading || isSubmitting}
                  className="p-1 hover:bg-muted rounded"
                >
                  <ChevronUp className="h-4 w-4" />
                </button>
                <button
                  type="button"
                  onClick={() => moveWaypoint(index, 'down')}
                  disabled={index === waypoints.length - 1 || isLoading || isSubmitting}
                  className="p-1 hover:bg-muted rounded"
                >
                  <ChevronDown className="h-4 w-4" />
                </button>
                <button
                  type="button"
                  onClick={() => removeWaypoint(index)}
                  disabled={isLoading || isSubmitting}
                  className="p-1 hover:bg-muted rounded text-red-500"
                >
                  <X className="h-4 w-4" />
                </button>
              </div>
              <Input
                value={waypoint.address}
                onChange={(e) => handleAddressChange(index, e.target.value)}
                placeholder={`Stop ${index + 1}`}
                className="pl-10"
                disabled={isLoading || isSubmitting}
              />
            </div>
          ))}

          <div>
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={addWaypoint}
              disabled={isLoading || isSubmitting}
              className="mt-1"
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Stop
            </Button>
          </div>

          <div>
            <Label htmlFor="endAddress">Destination</Label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                <MapPin className="h-4 w-4 text-muted-foreground" />
              </div>
              <Input
                id="endAddress"
                className="pl-10"
                placeholder="Destination"
                {...register('endAddress')}
                disabled={isLoading || isSubmitting}
              />
            </div>
            {errors.endAddress && (
              <p className="text-sm text-red-500 mt-1">{errors.endAddress.message}</p>
            )}
          </div>
        </div>

        <div>
          <Label htmlFor="notes">Notes (Optional)</Label>
          <Textarea
            id="notes"
            placeholder="Add any additional notes about this journey"
            {...register('notes')}
            disabled={isLoading || isSubmitting}
            rows={3}
          />
        </div>
      </div>

      <div className="flex justify-end space-x-3 pt-4">
        <Button
          type="button"
          variant="outline"
          onClick={() => navigate('/app/journeys')}
          disabled={isLoading || isSubmitting}
        >
          Cancel
        </Button>
        <Button type="submit" disabled={isLoading || isSubmitting}>
          {isLoading || isSubmitting ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              {journey ? 'Updating...' : 'Creating...'}
            </>
          ) : (
            <>{journey ? 'Update Journey' : 'Create Journey'}</>
          )}
        </Button>
      </div>
    </form>
  );
};

export default JourneyForm;

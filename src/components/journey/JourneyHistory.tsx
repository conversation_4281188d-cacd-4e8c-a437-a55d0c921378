import { useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import { formatDistanceToNow, parseISO } from 'date-fns';
import { MapPin, Clock, CheckCircle, Loader2, AlertCircle, ArrowRight, Plus } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Link } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { getJourneyHistory } from '@/services/journeyService';
import { useNotification } from '@/hooks/useNotification';

export const JourneyHistory = () => {
  const { error } = useNotification();
  const [page, setPage] = useState(1);
  const pageSize = 10;

  const {
    data: journeys,
    isLoading,
    isError,
    refetch,
  } = useQuery({
    queryKey: ['journeyHistory', page],
    queryFn: async () => {
      try {
        const data = await getJourneyHistory();
        // In a real app, we would handle pagination on the server
        const start = (page - 1) * pageSize;
        return data.slice(start, start + pageSize);
      } catch (err) {
        console.error('Error fetching journey history:', err);
        error('Error', 'Failed to load journey history');
        throw err;
      }
    },
    keepPreviousData: true,
  });

  if (isLoading && !journeys) {
    return (
      <div className="space-y-4">
        {[...Array(3)].map((_, i) => (
          <Card key={i} className="animate-pulse">
            <CardHeader>
              <Skeleton className="h-6 w-48 mb-2" />
              <Skeleton className="h-4 w-64" />
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-5/6" />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (isError) {
    return (
      <div className="flex flex-col items-center justify-center py-12 text-center">
        <AlertCircle className="h-12 w-12 text-red-500 mb-4" />
        <h3 className="text-lg font-medium mb-2">Failed to load journey history</h3>
        <p className="text-muted-foreground mb-4">There was an error loading your journey history. Please try again.</p>
        <Button onClick={() => refetch()} variant="outline">
          Retry
        </Button>
      </div>
    );
  }

  if (!journeys || journeys.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center py-12 text-center">
        <MapPin className="h-12 w-12 text-muted-foreground mb-4" />
        <h3 className="text-lg font-medium mb-2">No journeys yet</h3>
        <p className="text-muted-foreground mb-4">
          {journeys?.length === 0 
            ? 'Your completed journeys will appear here.'
            : 'No more journeys to show.'
          }
        </p>
        <Button asChild>
          <Link to="/app/journeys/new">
            <Plus className="mr-2 h-4 w-4" />
            {journeys?.length === 0 ? 'Create Your First Journey' : 'Create New Journey'}
          </Link>
        </Button>
      </div>
    );
  }

  const formatDuration = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.ceil((seconds % 3600) / 60);
    
    if (hours > 0) {
      return `${hours}h ${minutes}m`;
    }
    return `${minutes} min`;
  };

  const formatDistance = (meters: number): string => {
    if (meters < 1000) {
      return `${Math.round(meters)} m`;
    }
    return `${(meters / 1000).toFixed(1)} km`;
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold tracking-tight">Journey History</h2>
        <Button asChild>
          <Link to="/app/journeys/new">
            <Plus className="mr-2 h-4 w-4" />
            New Journey
          </Link>
        </Button>
      </div>
      <div className="space-y-4">
        {journeys.map((journey) => (
          <Card key={journey.id} className="hover:shadow-md transition-shadow">
            <CardHeader className="pb-3">
              <div className="flex justify-between items-start">
                <div>
                  <CardTitle className="text-lg">{journey.name}</CardTitle>
                  <CardDescription>
                    {formatDistanceToNow(parseISO(journey.startedAt), { addSuffix: true })}
                    {journey.completedAt && (
                      <span className="ml-2 inline-flex items-center text-xs text-green-600">
                        <CheckCircle className="h-3 w-3 mr-1" />
                        Completed
                      </span>
                    )}
                  </CardDescription>
                </div>
                <Button asChild variant="ghost" size="sm">
                  <Link to={`/app/journeys/${journey.id}`} className="flex items-center">
                    View Details
                    <ArrowRight className="ml-1 h-4 w-4" />
                  </Link>
                </Button>
              </div>
            </CardHeader>
            <CardContent className="pb-3">
              <div className="grid gap-4 md:grid-cols-3">
                <div className="space-y-1">
                  <p className="text-sm font-medium text-muted-foreground">From</p>
                  <p className="text-sm">{journey.startAddress}</p>
                </div>
                <div className="space-y-1">
                  <p className="text-sm font-medium text-muted-foreground">To</p>
                  <p className="text-sm">{journey.endAddress}</p>
                </div>
                <div className="space-y-1">
                  <p className="text-sm font-medium text-muted-foreground">Stops</p>
                  <p className="text-sm">{journey.totalStops} locations</p>
                </div>
              </div>
            </CardContent>
            <CardFooter className="bg-muted/50 py-2 px-4 text-sm">
              <div className="flex items-center space-x-4">
                <div className="flex items-center text-muted-foreground">
                  <Clock className="h-4 w-4 mr-1" />
                  <span>{formatDuration(journey.totalDuration)}</span>
                </div>
                <div className="flex items-center text-muted-foreground">
                  <MapPin className="h-4 w-4 mr-1" />
                  <span>{formatDistance(journey.totalDistance)}</span>
                </div>
              </div>
            </CardFooter>
          </Card>
        ))}
      </div>

      <div className="flex items-center justify-between">
        <Button
          variant="outline"
          size="sm"
          onClick={() => setPage((old) => Math.max(old - 1, 1))}
          disabled={page === 1}
        >
          Previous
        </Button>
        <span className="text-sm text-muted-foreground">
          Page {page}
        </span>
        <Button
          variant="outline"
          size="sm"
          onClick={() => setPage((old) => old + 1)}
          disabled={!journeys || journeys.length < pageSize}
        >
          Next
        </Button>
      </div>
    </div>
  );
};

export default JourneyHistory;

import { useParams, useNavigate, useSearchParams } from 'react-router-dom';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { useState, useCallback } from 'react';
import { format, parseISO } from 'date-fns';
import { MapPin, CheckCircle, ArrowLeft, AlertCircle, Clock as ClockIcon, Map as MapIcon, ListChecks, Flag, Edit, Play, List } from 'lucide-react';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { MapView } from '../../components/map/MapView';
import { Button } from '../../components/ui/button';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '../../components/ui/card';
// import type Skeleton from '@/components/ui/skeleton';
import { Skeleton } from '@/components/ui/skeleton';
import { RefreshCw } from 'lucide-react';
import { getJourney } from '@/services/journeyService';
import { useNotification } from '@/hooks/useNotification';

export const JourneyDetail = () => {
  const { id } = useParams<{ id: string; }>();
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useSearchParams({ tab: 'details' });
  const activeTab = searchParams.get('tab') || 'details';
  const queryClient = useQueryClient();
  const { error: showError, success } = useNotification();
  const [isJourneyStarted, setIsJourneyStarted] = useState(false);

  const setActiveTab = (tab: string) => {
    setSearchParams({ tab });
  };

  const {
    data: journey,
    isLoading,
    isError,
    refetch,
  } = useQuery({
    queryKey: ['journey', id],
    queryFn: () => getJourney(id!), // We know id exists because of the route
    enabled: !!id, // Only run the query if we have an ID
  });

  const formatDuration = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.ceil((seconds % 3600) / 60);

    if (hours > 0) {
      return `${hours}h ${minutes}m`;
    }
    return `${minutes} min`;
  };

  const formatDistance = (meters: number): string => {
    if (meters < 1000) {
      return `${Math.round(meters)} m`;
    }
    return `${(meters / 1000).toFixed(1)} km`;
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <Skeleton className="h-10 w-24" />
          <Skeleton className="h-10 w-32" />
        </div>
        <Card>
          <CardHeader>
            <Skeleton className="h-8 w-64 mb-2" />
            <Skeleton className="h-4 w-48" />
          </CardHeader>
          <CardContent className="space-y-4">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="space-y-2">
                <Skeleton className="h-4 w-32" />
                <Skeleton className="h-4 w-full" />
              </div>
            ))}
          </CardContent>
        </Card>
        <Card>
          <CardHeader>
            <Skeleton className="h-6 w-48" />
          </CardHeader>
          <CardContent className="space-y-4">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="flex items-center space-x-4">
                <Skeleton className="h-4 w-4 rounded-full" />
                <Skeleton className="h-4 flex-1" />
              </div>
            ))}
          </CardContent>
        </Card>
      </div>
    );
  }

  if (isError || !journey) {
    return (
      <div className="flex flex-col items-center justify-center py-12 text-center">
        <AlertCircle className="h-12 w-12 text-red-500 mb-4" />
        <h3 className="text-lg font-medium mb-2">Journey not found</h3>
        <p className="text-muted-foreground mb-4">
          The journey you're looking for may have been deleted or never existed.
        </p>
        <div className="flex space-x-2">
          <Button variant="outline" onClick={() => refetch()}>
            <RefreshCw className="mr-2 h-4 w-4" />
            Try Again
          </Button>
          <Button onClick={() => navigate('/journeys')}>Back to Journeys</Button>
        </div>
      </div>
    );
  }

  const startedAt = parseISO(journey.startedAt);
  const completedAt = journey.completedAt ? parseISO(journey.completedAt) : null;
  const allWaypoints = [
    { ...journey.waypoints[0], isStart: true, isEnd: false },
    ...journey.waypoints.slice(1, -1).map(wp => ({ ...wp, isStart: false, isEnd: false })),
    { ...journey.waypoints[journey.waypoints.length - 1], isStart: false, isEnd: true },
  ];

  const startJourney = useCallback(async () => {
    try {
      // In a real app, you would call an API to update the journey status
      // await api.startJourney(journey.id);
      setIsJourneyStarted(true);
      success('Journey started! Your optimized route is ready.');

      // Invalidate the journey query to refetch the updated data
      await queryClient.invalidateQueries({ queryKey: ['journey', id] });
    } catch (err) {
      console.error('Failed to start journey:', err);
      showError('Failed to start journey. Please try again.');
    }
  }, [journey?.id, queryClient, success, showError]);

  const handleRouteCalculated = useCallback((route: any) => {
    // You can use this callback to update the UI based on the calculated route
    console.log('Route calculated:', route);
  }, []);

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            size="icon"
            onClick={() => navigate(-1)}
            className="shrink-0"
          >
            <ArrowLeft className="h-4 w-4" />
            <span className="sr-only">Back</span>
          </Button>
          <div>
            <h1 className="text-2xl font-bold tracking-tight">
              {journey ? journey.name : 'Journey Details'}
            </h1>
            <p className="text-muted-foreground">
              {journey ? 'View and manage your journey details' : 'Loading journey details...'}
            </p>
          </div>
        </div>

        {journey && (
          <div className="flex space-x-2">
            <Button
              variant="outline"
              onClick={() => navigate(`/app/journeys/${journey.id}`)}
              disabled={activeTab === 'details'}
            >
              <List className="mr-2 h-4 w-4" />
              Details
            </Button>
            <Button
              variant="outline"
              onClick={() => navigate(`/app/journeys/${journey.id}?tab=map`)}
              disabled={activeTab === 'map'}
            >
              <MapIcon className="mr-2 h-4 w-4" />
              View on Map
            </Button>
            <Button
              variant="outline"
              onClick={() => navigate(`/app/journeys/${journey.id}/edit`)}
            >
              <Edit className="mr-2 h-4 w-4" />
              Edit
            </Button>
            <Button
              onClick={startJourney}
              disabled={isJourneyStarted || journey.completedAt !== null}
            >
              <Play className="mr-2 h-4 w-4" />
              {isJourneyStarted || journey.completedAt ? 'In Progress' : 'Start Journey'}
            </Button>
          </div>
        )}
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList>
          <TabsTrigger value="details">
            <List className="h-4 w-4 mr-2" />
            Details
          </TabsTrigger>
          <TabsTrigger value="map">
            <MapIcon className="h-4 w-4 mr-2" />
            Map View
          </TabsTrigger>
        </TabsList>

        <TabsContent value="details" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex flex-col md:flex-row md:items-center md:justify-between">
                <div>
                  <CardTitle className="text-2xl">{journey.name}</CardTitle>
                  <CardDescription className="mt-1">
                    {format(startedAt, 'MMMM d, yyyy')} • {format(startedAt, 'h:mm a')}
                    {completedAt && (
                      <span className="ml-2 inline-flex items-center text-sm text-green-600">
                        <CheckCircle className="h-4 w-4 mr-1" />
                        Completed {format(completedAt, 'MMM d, yyyy')}
                      </span>
                    )}
                  </CardDescription>
                </div>
                <div className="mt-2 md:mt-0 flex space-x-4">
                  <div className="flex items-center text-sm text-muted-foreground">
                    <ClockIcon className="h-4 w-4 mr-1" />
                    {formatDuration(journey.totalDuration)}
                  </div>
                  <div className="flex items-center text-sm text-muted-foreground">
                    <MapPin className="h-4 w-4 mr-1" />
                    {formatDistance(journey.totalDistance)}
                  </div>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid gap-6 md:grid-cols-2">
                <div>
                  <h3 className="text-lg font-medium mb-4 flex items-center">
                    <MapPin className="h-5 w-5 mr-2 text-primary" />
                    Route Overview
                  </h3>
                  <div className="space-y-4">
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">Start</p>
                      <p>{journey.startAddress}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">End</p>
                      <p>{journey.endAddress}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">Total Stops</p>
                      <p>{journey.waypoints.length} locations</p>
                    </div>
                  </div>
                </div>
                <div>
                  <h3 className="text-lg font-medium mb-4 flex items-center">
                    <ListChecks className="h-5 w-5 mr-2 text-primary" />
                    Stops ({journey.waypoints.length})
                  </h3>
                  <div className="border rounded-lg divide-y max-h-96 overflow-y-auto">
                    {allWaypoints.map((waypoint, index) => (
                      <div key={waypoint.id} className="p-3 flex items-start">
                        <div className="flex-shrink-0 h-8 w-8 rounded-full bg-muted flex items-center justify-center mr-3 mt-0.5">
                          {waypoint.isStart ? (
                            <MapPin className="h-4 w-4 text-green-500" />
                          ) : waypoint.isEnd ? (
                            <Flag className="h-4 w-4 text-red-500" />
                          ) : (
                            <span className="text-xs font-medium text-muted-foreground">{index}</span>
                          )}
                        </div>
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium truncate">
                            {waypoint.isStart ? 'Start' : waypoint.isEnd ? 'Destination' : `Stop ${index}`}
                          </p>
                          <p className="text-sm text-muted-foreground truncate">{waypoint.address}</p>
                        </div>
                        {waypoint.completed && (
                          <CheckCircle className="h-4 w-4 text-green-500 ml-2 flex-shrink-0" />
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="map" className="h-[600px] rounded-lg overflow-hidden">
          <MapView
            journey={journey}
            className="h-full"
            onJourneyStart={startJourney}
            isJourneyStarted={isJourneyStarted || journey.completedAt !== null}
            onRouteCalculated={handleRouteCalculated}
          />
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default JourneyDetail;

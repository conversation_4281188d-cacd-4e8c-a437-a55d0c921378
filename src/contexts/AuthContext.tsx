import React, { createContext, useContext, useEffect, useState, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { Session, User, supabase } from '../lib/supabase';
import { toast } from '../components/ui/use-toast';

interface AuthContextType {
  user: User | null;
  session: Session | null;
  loading: boolean;
  isPhoneVerified: boolean;
  signIn: (email: string, password: string) => Promise<{
    error: Error | null;
    data: Session | null;
  }>;
  signUp: (email: string, password: string, fullName: string, phone?: string) => Promise<{
    error: Error | null;
    data: Session | null;
  }>;
  signOut: () => Promise<void>;
  resetPassword: (email: string) => Promise<{ error: Error | null }>;
  updatePassword: (newPassword: string) => Promise<{ error: Error | null }>;
  updateProfile: (updates: { full_name?: string; phone?: string }) => Promise<{ error: Error | null }>;
  sendPhoneVerification: (phone: string) => Promise<{ error: Error | null; message?: string }>;
  verifyPhone: (code: string) => Promise<{ error: Error | null; verified: boolean }>;
  checkPhoneVerification: () => Promise<boolean>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [loading, setLoading] = useState(true);
  const [isPhoneVerified, setIsPhoneVerified] = useState(false);
  const navigate = useNavigate();

  useEffect(() => {
    // Check active sessions and sets the user
    const getSession = async () => {
      const { data: { session }, error } = await supabase.auth.getSession();
      if (error) console.error('Error getting session:', error);
      
      setSession(session);
      setUser(session?.user ?? null);
      setLoading(false);
    };

    getSession();

    // Listen for changes in auth state
    const { data: { subscription } } = supabase.auth.onAuthStateChange(async (event, session) => {
      setSession(session);
      setUser(session?.user ?? null);
      setLoading(false);

      if (event === 'SIGNED_IN') {
        navigate('/app');
      } else if (event === 'SIGNED_OUT') {
        navigate('/login');
      }
    });

    return () => {
      subscription?.unsubscribe();
    };
  }, [navigate]);

  const signIn = async (email: string, password: string) => {
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });
    return { data, error };
  };

  const signUp = async (email: string, password: string, fullName: string, phone?: string) => {
    try {
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            full_name: fullName,
            phone,
          },
        },
      });

      if (error) throw error;
      return { error: null, data };
    } catch (error) {
      return { error: error as Error, data: null };
    }
  };

  const signOut = async () => {
    await supabase.auth.signOut();
  };

  const resetPassword = async (email: string) => {
    const { error } = await supabase.auth.resetPasswordForEmail(email, {
      redirectTo: `${window.location.origin}/reset-password`,
    });
    return { error };
  };

  const updatePassword = async (newPassword: string) => {
    const { error } = await supabase.auth.updateUser({
      password: newPassword,
    });
    return { error };
  };

  const updateProfile = async (updates: { full_name?: string; phone?: string }) => {
    const { error } = await supabase
      .from('profiles')
      .upsert({
        id: user?.id,
        ...updates,
        updated_at: new Date().toISOString(),
      });
    
    if (!error && updates.full_name) {
      await supabase.auth.updateUser({
        data: { full_name: updates.full_name },
      });
    }

    return { error };
  };

  const sendPhoneVerification = async (phone: string) => {
    try {
      const { error } = await supabase.auth.signInWithOtp({
        phone,
      });

      if (error) throw error;
      
      return { 
        error: null, 
        message: 'Verification code sent to your phone' 
      };
    } catch (error) {
      console.error('Error sending verification code:', error);
      return { 
        error: error as Error, 
        message: 'Failed to send verification code' 
      };
    }
  };

  const verifyPhone = async (code: string) => {
    try {
      const { data, error } = await supabase.auth.verifyOtp({
        phone: user?.phone || '',
        token: code,
        type: 'sms',
      });

      if (error) throw error;
      
      // Update user's phone verification status in your database
      const { error: updateError } = await supabase
        .from('profiles')
        .update({ phone_verified: true })
        .eq('id', user?.id);

      if (updateError) throw updateError;
      
      setIsPhoneVerified(true);
      return { error: null, verified: true };
    } catch (error) {
      console.error('Error verifying phone:', error);
      return { 
        error: error as Error, 
        verified: false 
      };
    }
  };

  const checkPhoneVerification = async () => {
    if (!user) return false;
    
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('phone_verified')
        .eq('id', user.id)
        .single();

      if (error) throw error;
      
      const verified = data?.phone_verified || false;
      setIsPhoneVerified(verified);
      return verified;
    } catch (error) {
      console.error('Error checking phone verification:', error);
      return false;
    }
  };

  // Check phone verification status when user changes
  useEffect(() => {
    if (user) {
      checkPhoneVerification();
    }
  }, [user]);

  const value = {
    user,
    session,
    loading,
    isPhoneVerified,
    signIn,
    signUp,
    signOut,
    resetPassword,
    updatePassword,
    updateProfile,
    sendPhoneVerification,
    verifyPhone,
    checkPhoneVerification,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

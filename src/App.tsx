import { useState } from 'react';
import { Navigation, RotateCcw, Save, Loader2, ArrowLeft, Clock, ArrowRight } from 'lucide-react';
import { Link } from 'react-router-dom';
import { useJourneyStore } from './store/useJourneyStore';
import { useJourneyMutations } from './services/api';
import { NotificationProvider } from './contexts/NotificationContext';
import { NotificationContainer } from './components/notifications/NotificationContainer';

interface RouteLeg {
  from: string;
  to: string;
  distance: string;
  duration: string;
}

interface OptimizedRoute {
  totalDistance: string;
  totalDuration: string;
  legs: RouteLeg[];
  optimizedOrder: string[];
}

// Get the Google Maps API key from environment variables
const GOOGLE_MAPS_API_KEY = import.meta.env.VITE_GOOGLE_MAPS_API_KEY || '';
import AddressInput from './components/AddressInput';
import LocationList from './components/LocationList';

const AppContent = () => {
  const {
    startAddress,
    currentAddress,
    locations,
    setStartAddress,
    setCurrentAddress,
    addLocation,
    removeLocation,
    reorderLocations,
    reset,
  } = useJourneyStore();

  const { createJourneyWithStops } = useJourneyMutations();
  const isSaving = createJourneyWithStops.isPending;
  const [isOptimizing, setIsOptimizing] = useState(false);
  const [optimizedRoute, setOptimizedRoute] = useState<OptimizedRoute | null>(null);

  const handleAddLocation = () => {
    if (currentAddress.trim()) {
      addLocation({ address: currentAddress });
    }
  };

  const simulateRouteOptimization = (waypoints: string[]): OptimizedRoute => {
    // This is a mock implementation that would be replaced with actual API call
    const distances = ["2.5 mi", "1.8 mi", "3.2 mi"];
    const durations = ["8 min", "6 min", "10 min"];
    
    const legs: RouteLeg[] = [];
    const allPoints = [startAddress, ...waypoints];
    
    for (let i = 0; i < allPoints.length - 1; i++) {
      legs.push({
        from: allPoints[i],
        to: allPoints[i + 1],
        distance: distances[i % distances.length],
        duration: durations[i % durations.length]
      });
    }
    
    return {
      totalDistance: (legs.reduce((sum, leg) => sum + parseFloat(leg.distance), 0)).toFixed(1) + ' mi',
      totalDuration: legs.reduce((sum, leg) => sum + parseInt(leg.duration), 0) + ' min',
      legs,
      optimizedOrder: [...allPoints]
    };
  };

  const handleOptimize = async () => {
    if (locations.length < 1) {
      alert('Please add at least one stop to optimize');
      return;
    }
    
    setIsOptimizing(true);
    try {
      // Simulate API call with timeout
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // Get mock optimized route
      const waypoints = locations.map(loc => loc.address);
      const result = simulateRouteOptimization(waypoints);
      setOptimizedRoute(result);
      
    } catch (error) {
      console.error('Error optimizing route:', error);
      alert('Failed to optimize route. Please try again.');
    } finally {
      setIsOptimizing(false);
    }
  };

  const handleSave = async () => {
    if (!startAddress.trim()) {
      alert('Please enter a starting point');
      return;
    }

    if (locations.length === 0) {
      alert('Please add at least one stop');
      return;
    }

    try {
      await createJourneyWithStops.mutateAsync({
        journey: { start_address: startAddress },
        stops: locations.map((loc, index) => ({
          address: loc.address,
          position: index
        }))
      });
      
      alert('Journey saved successfully!');
      reset();
    } catch (error) {
      console.error('Error saving journey:', error);
      alert('Failed to save journey. Please try again.');
    }
  };

  const handleReset = () => {
    if (window.confirm('Are you sure you want to reset the form? This will clear all your current inputs.')) {
      reset();
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-50">
      <div className="max-w-4xl mx-auto p-6">
        <div className="flex justify-between items-center mb-8">
          <Link to="/" className="text-blue-600 hover:text-blue-800 flex items-center">
            <ArrowLeft size={20} className="mr-1" /> Back to Home
          </Link>
          <h1 className="text-2xl font-bold text-gray-900">Journey Optimizer</h1>
          <div className="w-8"></div> {/* Spacer for alignment */}
        </div>
        
        {optimizedRoute && (
          <div className="mb-8 bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-lg font-semibold text-blue-800">Optimized Route</h2>
              <div className="flex items-center text-sm text-blue-700">
                <Clock className="mr-1 h-4 w-4" />
                <span className="mr-4">{optimizedRoute.totalDuration}</span>
                <Navigation className="mr-1 h-4 w-4" />
                <span>{optimizedRoute.totalDistance}</span>
              </div>
            </div>
            
            <div className="space-y-3">
              {optimizedRoute.legs.map((leg, index) => (
                <div key={index} className="flex items-start">
                  <div className="flex-shrink-0 w-6 h-6 rounded-full bg-blue-100 text-blue-700 flex items-center justify-center text-xs font-medium mt-0.5">
                    {index + 1}
                  </div>
                  <div className="ml-3">
                    <div className="text-sm font-medium text-gray-900">{leg.from}</div>
                    <div className="mt-1 flex items-center text-xs text-gray-500">
                      <ArrowRight className="h-3 w-3 mr-1 text-blue-500" />
                      {leg.distance} • {leg.duration}
                    </div>
                  </div>
                </div>
              ))}
              {optimizedRoute.legs.length > 0 && (
                <div className="flex items-center mt-1 ml-9">
                  <div className="w-4 h-4 rounded-full bg-green-100 text-green-700 flex items-center justify-center text-xs font-medium">
                    ✓
                  </div>
                  <div className="ml-3 text-sm font-medium text-gray-900">
                    {optimizedRoute.legs[optimizedRoute.legs.length - 1].to}
                  </div>
                </div>
              )}
            </div>
          </div>
        )}
        
        <div className="bg-white rounded-xl shadow-lg p-6 mb-6">
          <p className="text-gray-600 mb-6 text-center">Plan and optimize your multi-stop journey efficiently</p>

        <div className="bg-white rounded-xl shadow-lg p-6 mb-6">
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Starting Point
            </label>
            <AddressInput
              placeholder="Enter starting address"
              onSelect={setStartAddress}
              value={startAddress}
              apiKey={GOOGLE_MAPS_API_KEY}
            />
          </div>

          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Add Stop
            </label>
            <div className="flex gap-2">
              <AddressInput
                placeholder="Enter location"
                onSelect={setCurrentAddress}
                value={currentAddress}
                apiKey={GOOGLE_MAPS_API_KEY}
              />
              <button
                onClick={handleAddLocation}
                className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
              >
                Add
              </button>
            </div>
          </div>

          {locations.length > 0 && (
            <div className="mb-6">
              <h3 className="text-lg font-medium text-gray-900 mb-3">Stops</h3>
              <LocationList
                locations={locations}
                onReorder={reorderLocations}
                onRemove={removeLocation}
              />
            </div>
          )}

          <div className="flex gap-3">
            <button
              onClick={handleOptimize}
              disabled={isOptimizing || isSaving}
              className={`flex-1 px-4 py-2 ${isOptimizing ? 'bg-green-600' : 'bg-green-500 hover:bg-green-600'} text-white rounded-lg transition-colors flex items-center justify-center gap-2 disabled:opacity-50`}
            >
              {isOptimizing ? (
                <>
                  <Loader2 className="animate-spin" size={20} />
                  Optimizing...
                </>
              ) : (
                <>
                  <Navigation size={20} />
                  Optimize Route
                </>
              )}
            </button>
            <button
              onClick={handleSave}
              disabled={isSaving || isOptimizing}
              className={`px-4 py-2 ${isSaving ? 'bg-blue-600' : 'bg-blue-500 hover:bg-blue-600'} text-white rounded-lg transition-colors flex items-center gap-2 disabled:opacity-50`}
            >
              {isSaving ? (
                <>
                  <Loader2 className="animate-spin" size={20} />
                  Saving...
                </>
              ) : (
                <>
                  <Save size={20} />
                  Save
                </>
              )}
            </button>
            <button
              onClick={handleReset}
              className="px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors flex items-center gap-2"
            >
              <RotateCcw size={20} />
              Reset
            </button>
          </div>
        </div>
        </div>
      </div>
    </div>
  );
}

const App = () => {
  return (
    <NotificationProvider>
      <AppContent />
      <NotificationContainer />
    </NotificationProvider>
  );
};

export default App;
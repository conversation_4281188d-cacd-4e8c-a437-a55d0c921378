const TRIAL_KEY = 'quick_journey_trial';

export interface TrialData {
  startDate: string;
  endDate: string;
  activated: boolean;
}

export const startTrial = (): TrialData => {
  const startDate = new Date();
  const endDate = new Date();
  endDate.setDate(startDate.getDate() + 14); // 14-day trial

  const trialData: TrialData = {
    startDate: startDate.toISOString(),
    endDate: endDate.toISOString(),
    activated: true
  };

  localStorage.setItem(TRIAL_KEY, JSON.stringify(trialData));
  return trialData;
};

export const getTrialData = (): TrialData | null => {
  const trialData = localStorage.getItem(TRIAL_KEY);
  return trialData ? JSON.parse(trialData) : null;
};

export const isTrialActive = (): boolean => {
  const trialData = getTrialData();
  if (!trialData) return false;
  
  const now = new Date();
  const endDate = new Date(trialData.endDate);
  return trialData.activated && now <= endDate;
};

export const getDaysRemaining = (): number | null => {
  const trialData = getTrialData();
  if (!trialData) return null;
  
  const now = new Date();
  const endDate = new Date(trialData.endDate);
  
  if (now > endDate) return 0;
  
  const diffTime = endDate.getTime() - now.getTime();
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
};

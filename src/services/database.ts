import Database from 'better-sqlite3';
import path from 'path';

// Initialize the database
const dbPath = path.join(process.cwd(), 'journey.db');
const db = new Database(dbPath);

// Create tables if they don't exist
db.prepare(`
  CREATE TABLE IF NOT EXISTS journeys (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    start_address TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
  )
`).run();

db.prepare(`
  CREATE TABLE IF NOT EXISTS stops (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    journey_id INTEGER,
    address TEXT NOT NULL,
    position INTEGER NOT NULL,
    FOREIGN KEY (journey_id) REFERENCES journeys (id) ON DELETE CASCADE
  )
`).run();



export interface Journey {
  id?: number;
  start_address: string;
  created_at?: string;
}

export interface Stop {
  id?: number;
  journey_id: number;
  address: string;
  position: number;
}

export const database = {
  // Journey operations
  createJourney(journey: Omit<Journey, 'id' | 'created_at'>) {
    const stmt = db.prepare('INSERT INTO journeys (start_address) VALUES (?)');
    const info = stmt.run(journey.start_address);
    return info.lastInsertRowid as number;
  },

  getJourney(id: number) {
    const stmt = db.prepare('SELECT * FROM journeys WHERE id = ?');
    return stmt.get(id) as Journey | undefined;
  },

  // Stop operations
  addStops(stops: Omit<Stop, 'id'>[]) {
    const stmt = db.prepare(
      'INSERT INTO stops (journey_id, address, position) VALUES (?, ?, ?)'
    );
    
    const insertMany = db.transaction((stops: Omit<Stop, 'id'>[]) => {
      for (const stop of stops) {
        stmt.run(stop.journey_id, stop.address, stop.position);
      }
    });
    
    insertMany(stops);
  },

  getStopsByJourneyId(journeyId: number) {
    const stmt = db.prepare(
      'SELECT * FROM stops WHERE journey_id = ? ORDER BY position ASC'
    );
    return stmt.all(journeyId) as Stop[];
  },

  // Combined operations
  saveJourneyWithStops(
    journey: Omit<Journey, 'id' | 'created_at'>,
    stops: Omit<Stop, 'id' | 'journey_id'>[]
  ) {
    try {
      return db.transaction(() => {
        // Create the journey
        const journeyStmt = db.prepare('INSERT INTO journeys (start_address) VALUES (?)');
        const journeyInfo = journeyStmt.run(journey.start_address);
        const journeyId = journeyInfo.lastInsertRowid as number;
        
        // Add all stops
        if (stops.length > 0) {
          const stopsStmt = db.prepare(
            'INSERT INTO stops (journey_id, address, position) VALUES (?, ?, ?)'
          );
          
          for (let i = 0; i < stops.length; i++) {
            stopsStmt.run(journeyId, stops[i].address, i);
          }
        }
        
        return journeyId;
      })();
    } catch (error) {
      console.error('Transaction failed:', error);
      throw error;
    }
  },
};

export default database;

import { mockDb } from './mockDatabase';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';

export interface Journey {
  id: number;
  start_address: string;
  created_at: string;
}

export interface Stop {
  id: number;
  journey_id: number;
  address: string;
  position: number;
}

export const useJourneyQueries = () => {
  // Get all journeys
  const useGetJourneys = () => {
    return useQuery<Journey[]>({
      queryKey: ['journeys'],
      queryFn: async () => mockDb.getAllJourneys(),
    });
  };

  // Get a journey by ID
  const useGetJourney = (journeyId: number) => {
    return useQuery<Journey | null>({
      queryKey: ['journey', journeyId],
      queryFn: async () => mockDb.getJourney(journeyId) || null,
      enabled: !!journeyId,
    });
  };

  // Get stops for a journey
  const useGetStops = (journeyId: number) => {
    return useQuery<Stop[]>({
      queryKey: ['stops', journeyId],
      queryFn: async () => mockDb.getStopsByJourneyId(journeyId),
      enabled: !!journeyId,
    });
  };

  return {
    useGetJourneys,
    useGetJourney,
    useGetStops,
  };
};

export const useJourneyMutations = () => {
  const queryClient = useQueryClient();

  // Create a new journey with stops
  const createJourneyWithStops = useMutation({
    mutationFn: async (data: { journey: Omit<Journey, 'id' | 'created_at'>; stops: Array<Omit<Stop, 'id' | 'journey_id'>> }) => {
      return mockDb.saveJourneyWithStops(data.journey, data.stops);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['journeys'] });
    },
  });

  return { createJourneyWithStops };
};

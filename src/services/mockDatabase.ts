interface Journey {
  id: number;
  start_address: string;
  created_at: string;
}

interface Stop {
  id: number;
  journey_id: number;
  address: string;
  position: number;
}

class MockDatabase {
  private journeys: Journey[] = [];
  private stops: Stop[] = [];
  private journeyIdCounter = 1;
  private stopIdCounter = 1;

  async saveJourneyWithStops(journey: Omit<Journey, 'id' | 'created_at'>, stops: Array<Omit<Stop, 'id' | 'journey_id'>>): Promise<number> {
    return new Promise((resolve) => {
      // Simulate database delay
      setTimeout(() => {
        const newJourney: Journey = {
          id: this.journeyIdCounter++,
          start_address: journey.start_address,
          created_at: new Date().toISOString()
        };
        
        this.journeys.push(newJourney);
        
        const newStops = stops.map((stop, index) => ({
          id: this.stopIdCounter++,
          journey_id: newJourney.id,
          address: stop.address,
          position: stop.position
        }));
        
        this.stops.push(...newStops);
        
        console.log('Saved journey:', { journey: newJourney, stops: newStops });
        resolve(newJourney.id);
      }, 300);
    });
  }
  
  async getJourney(id: number): Promise<Journey | null> {
    return new Promise<Journey | null>((resolve) => {
      setTimeout(() => {
        resolve(this.journeys.find(j => j.id === id) || null);
      }, 200);
    });
  }
  
  async getStopsByJourneyId(journeyId: number) {
    return new Promise<Stop[]>((resolve) => {
      setTimeout(() => {
        resolve(this.stops.filter(s => s.journey_id === journeyId));
      }, 200);
    });
  }
  
  async getAllJourneys() {
    return new Promise<Journey[]>((resolve) => {
      setTimeout(() => {
        resolve([...this.journeys]);
      }, 200);
    });
  }
}

export const mockDb = new MockDatabase();

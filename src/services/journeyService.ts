import { supabase } from '@/lib/supabase';
import { Journey, JourneySummary, CreateJourneyInput, UpdateJourneyInput } from '@/types/journey';

export const createJourney = async (input: CreateJourneyInput): Promise<Journey> => {
  const { data, error } = await supabase
    .from('journeys')
    .insert({
      name: input.name,
      start_address: input.startAddress,
      end_address: input.endAddress,
      waypoints: input.waypoints.map(wp => ({
        address: wp.address,
        order: wp.order,
        completed: false,
      })),
      total_distance: 0, // Will be calculated after route optimization
      total_duration: 0, // Will be calculated after route optimization
    })
    .select('*')
    .single();

  if (error) throw error;
  return mapJourneyFromDB(data);
};

export const getJourney = async (id: string): Promise<Journey> => {
  const { data, error } = await supabase
    .from('journeys')
    .select('*')
    .eq('id', id)
    .single();

  if (error) throw error;
  return mapJourneyFromDB(data);
};

export const getJourneyHistory = async (): Promise<JourneySummary[]> => {
  const { data, error } = await supabase
    .from('journeys')
    .select('id, name, start_address, end_address, total_distance, total_duration, started_at, completed_at, created_at, updated_at, waypoints')
    .order('created_at', { ascending: false });

  if (error) throw error;

  return data.map(journey => ({
    id: journey.id,
    name: journey.name,
    startAddress: journey.start_address,
    endAddress: journey.end_address,
    totalStops: journey.waypoints?.length || 0,
    totalDistance: journey.total_distance || 0,
    totalDuration: journey.total_duration || 0,
    startedAt: journey.started_at,
    completedAt: journey.completed_at,
    createdAt: journey.created_at,
    updatedAt: journey.updated_at,
  }));
};

export const updateJourney = async (id: string, updates: UpdateJourneyInput): Promise<Journey> => {
  const updateData: any = {};
  
  if (updates.name) updateData.name = updates.name;
  if (updates.startAddress) updateData.start_address = updates.startAddress;
  if (updates.endAddress) updateData.end_address = updates.endAddress;
  if (updates.waypoints) {
    updateData.waypoints = updates.waypoints.map(wp => ({
      id: wp.id || crypto.randomUUID(),
      address: wp.address,
      order: wp.order,
      completed: wp.completed || false,
    }));
  }
  if (updates.completed !== undefined) {
    updateData.completed_at = updates.completed ? new Date().toISOString() : null;
  }

  const { data, error } = await supabase
    .from('journeys')
    .update(updateData)
    .eq('id', id)
    .select('*')
    .single();

  if (error) throw error;
  return mapJourneyFromDB(data);
};

export const deleteJourney = async (id: string): Promise<void> => {
  const { error } = await supabase
    .from('journeys')
    .delete()
    .eq('id', id);

  if (error) throw error;
};

// Helper function to map database fields to our Journey type
const mapJourneyFromDB = (data: any): Journey => ({
  id: data.id,
  userId: data.user_id,
  name: data.name,
  startAddress: data.start_address,
  endAddress: data.end_address,
  waypoints: (data.waypoints || []).map((wp: any) => ({
    id: wp.id,
    address: wp.address,
    order: wp.order,
    completed: wp.completed || false,
  })),
  totalDistance: data.total_distance || 0,
  totalDuration: data.total_duration || 0,
  startedAt: data.started_at,
  completedAt: data.completed_at,
  createdAt: data.created_at,
  updatedAt: data.updated_at,
});

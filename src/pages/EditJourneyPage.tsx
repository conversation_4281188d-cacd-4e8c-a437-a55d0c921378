import { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { ArrowLeft, Loader2 } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { JourneyForm } from '@/components/journey/JourneyForm';
import { useNotification } from '@/hooks/useNotification';
import { getJourney, updateJourney } from '@/services/journeyService';
import { Journey } from '@/types/journey';

export const EditJourneyPage = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { success, error } = useNotification();
  const [journey, setJourney] = useState<Journey | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    const fetchJourney = async () => {
      if (!id) return;
      
      try {
        const data = await get<PERSON><PERSON><PERSON>(id);
        setJourney(data);
      } catch (err) {
        console.error('Error fetching journey:', err);
        error('Error', 'Failed to load journey. Please try again.');
        navigate('/app/journeys');
      } finally {
        setIsLoading(false);
      }
    };

    fetchJourney();
  }, [id, navigate, error]);

  const handleSubmit = async (data: any) => {
    if (!id) return;
    
    try {
      setIsSubmitting(true);
      await updateJourney(id, data);
      success('Journey Updated', 'Your journey has been updated successfully!');
      navigate(`/app/journeys/${id}`);
    } catch (err) {
      console.error('Error updating journey:', err);
      error('Error', 'Failed to update journey. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
      </div>
    );
  }

  if (!journey) {
    return (
      <div className="text-center py-12">
        <h2 className="text-xl font-semibold">Journey not found</h2>
        <p className="text-muted-foreground mt-2">The journey you're looking for doesn't exist or has been deleted.</p>
        <Button className="mt-4" onClick={() => navigate('/app/journeys')}>
          Back to Journeys
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center space-x-4">
        <Button
          variant="outline"
          size="icon"
          onClick={() => navigate(-1)}
          className="shrink-0"
        >
          <ArrowLeft className="h-4 w-4" />
          <span className="sr-only">Back</span>
        </Button>
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Edit Journey</h1>
          <p className="text-muted-foreground">
            Update your journey details and route
          </p>
        </div>
      </div>

      <div className="bg-card rounded-lg border p-6 shadow-sm">
        <JourneyForm 
          journey={journey} 
          onSubmit={handleSubmit} 
          isSubmitting={isSubmitting} 
        />
      </div>
    </div>
  );
};

export default EditJourneyPage;

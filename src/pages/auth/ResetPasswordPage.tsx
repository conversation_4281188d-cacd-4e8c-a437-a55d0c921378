import { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { AuthLayout } from '@/layouts/AuthLayout';
import { ResetPasswordForm } from '@/components/auth/ResetPasswordForm';

export const ResetPasswordPage = () => {
  const { user } = useAuth();
  const navigate = useNavigate();

  useEffect(() => {
    if (user) {
      // Redirect to app if already logged in
      navigate('/app');
    }
  }, [user, navigate]);

  return (
    <AuthLayout
      title="Create a new password"
      description="Enter a new password for your account."
    >
      <ResetPasswordForm />
    </AuthLayout>
  );
};

export default ResetPasswordPage;

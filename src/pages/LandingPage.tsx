import { Link } from 'react-router-dom';
import { Navigation, Clock, Zap, Bar<PERSON>hart2, MapPin, Truck, Package, CheckCircle } from 'lucide-react';

export default function LandingPage() {
  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <div className="bg-gradient-to-r from-blue-600 to-indigo-700 text-white">
        <div className="container mx-auto px-4 py-20 text-center">
          <h1 className="text-4xl md:text-6xl font-bold mb-6">Optimize Your Delivery Routes</h1>
          <p className="text-xl md:text-2xl mb-6 max-w-3xl mx-auto">
            Save time, reduce costs, and improve efficiency with our intelligent route planning solution
            for couriers, delivery services, and field teams.
          </p>
          <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-8 max-w-2xl mx-auto rounded">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-yellow-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-sm text-yellow-700">
                  <span className="font-bold">14-day free trial</span> - No credit card required. Start optimizing your routes today!
                </p>
              </div>
            </div>
          </div>
          <div className="flex flex-col sm:flex-row justify-center gap-4">
            <Link
              to="/app"
              className="bg-white text-blue-600 hover:bg-blue-50 px-8 py-3 rounded-lg font-semibold text-lg transition-colors"
            >
              Start Your Free Trial
            </Link>
            <a
              href="#features"
              className="bg-transparent border-2 border-white hover:bg-white/10 px-8 py-3 rounded-lg font-semibold text-lg transition-colors"
            >
              Learn More
            </a>
          </div>
        </div>
      </div>

      {/* Features Section */}
      <div id="features" className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold text-center mb-16">Powerful Features for Your Business</h2>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {/* Feature 1 */}
            <div className="bg-white p-6 rounded-xl shadow-lg hover:shadow-xl transition-shadow">
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                <Navigation className="text-blue-600" size={24} />
              </div>
              <h3 className="text-xl font-semibold mb-2">Smart Route Optimization</h3>
              <p className="text-gray-600">
                Automatically calculates the most efficient route to save time and fuel costs.
              </p>
            </div>

            {/* Feature 2 */}
            <div className="bg-white p-6 rounded-xl shadow-lg hover:shadow-xl transition-shadow">
              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-4">
                <Clock className="text-green-600" size={24} />
              </div>
              <h3 className="text-xl font-semibold mb-2">Time-Saving</h3>
              <p className="text-gray-600">
                Reduce delivery times by up to 30% with our intelligent algorithms.
              </p>
            </div>

            {/* Feature 3 */}
            <div className="bg-white p-6 rounded-xl shadow-lg hover:shadow-xl transition-shadow">
              <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mb-4">
                <Zap className="text-purple-600" size={24} />
              </div>
              <h3 className="text-xl font-semibold mb-2">Real-Time Updates</h3>
              <p className="text-gray-600">
                Get live traffic updates and adjust routes on the fly for maximum efficiency.
              </p>
            </div>

            {/* Feature 4 */}
            <div className="bg-white p-6 rounded-xl shadow-lg hover:shadow-xl transition-shadow">
              <div className="w-12 h-12 bg-amber-100 rounded-lg flex items-center justify-center mb-4">
                <BarChart2 className="text-amber-600" size={24} />
              </div>
              <h3 className="text-xl font-semibold mb-2">Analytics Dashboard</h3>
              <p className="text-gray-600">
                Track performance metrics and identify areas for improvement.
              </p>
            </div>

            {/* Feature 5 */}
            <div className="bg-white p-6 rounded-xl shadow-lg hover:shadow-xl transition-shadow">
              <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center mb-4">
                <MapPin className="text-red-600" size={24} />
              </div>
              <h3 className="text-xl font-semibold mb-2">Multi-Stop Planning</h3>
              <p className="text-gray-600">
                Easily plan and optimize routes with multiple destinations.
              </p>
            </div>

            {/* Feature 6 */}
            <div className="bg-white p-6 rounded-xl shadow-lg hover:shadow-xl transition-shadow">
              <div className="w-12 h-12 bg-teal-100 rounded-lg flex items-center justify-center mb-4">
                <Truck className="text-teal-600" size={24} />
              </div>
              <h3 className="text-xl font-semibold mb-2">Fleet Management</h3>
              <p className="text-gray-600">
                Manage multiple vehicles and drivers from a single platform.
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* How It Works */}
      <div className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold text-center mb-16">How It Works</h2>
          
          <div className="max-w-4xl mx-auto">
            <div className="relative">
              {/* Timeline */}
              <div className="hidden md:block absolute left-1/2 top-0 bottom-0 w-1 bg-gray-200 transform -translate-x-1/2"></div>
              
              {/* Step 1 */}
              <div className="relative mb-16 md:flex items-center">
                <div className="md:w-1/2 md:pr-12 mb-6 md:mb-0 text-right">
                  <h3 className="text-2xl font-semibold mb-2">1. Add Your Stops</h3>
                  <p className="text-gray-600">
                    Enter your starting point and add all your delivery or service locations.
                  </p>
                </div>
                <div className="hidden md:block w-16 h-16 rounded-full bg-blue-100 flex items-center justify-center mx-auto flex-shrink-0 z-10">
                  <Package className="text-blue-600" size={28} />
                </div>
                <div className="md:w-1/2 md:pl-12"></div>
              </div>

              {/* Step 2 */}
              <div className="relative mb-16 md:flex flex-row-reverse items-center">
                <div className="md:w-1/2 md:pl-12 mb-6 md:mb-0">
                  <h3 className="text-2xl font-semibold mb-2">2. Optimize Your Route</h3>
                  <p className="text-gray-600">
                    Let our algorithm calculate the most efficient route for all your stops.
                  </p>
                </div>
                <div className="hidden md:block w-16 h-16 rounded-full bg-green-100 flex items-center justify-center mx-auto flex-shrink-0 z-10">
                  <Zap className="text-green-600" size={28} />
                </div>
                <div className="md:w-1/2 md:pr-12"></div>
              </div>

              {/* Step 3 */}
              <div className="relative md:flex items-center">
                <div className="md:w-1/2 md:pr-12 mb-6 md:mb-0 text-right">
                  <h3 className="text-2xl font-semibold mb-2">3. Hit the Road</h3>
                  <p className="text-gray-600">
                    Follow the optimized route and complete your deliveries efficiently.
                  </p>
                </div>
                <div className="hidden md:block w-16 h-16 rounded-full bg-purple-100 flex items-center justify-center mx-auto flex-shrink-0 z-10">
                  <CheckCircle className="text-purple-600" size={28} />
                </div>
                <div className="md:w-1/2 md:pl-12"></div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* CTA Section */}
      <div className="bg-gradient-to-r from-blue-600 to-indigo-700 text-white py-16">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-6">Ready to Transform Your Deliveries?</h2>
          <p className="text-xl mb-8 max-w-2xl mx-auto">
            Join thousands of businesses that trust our route optimization platform to save time and money.
          </p>
          <Link
            to="/app"
            className="inline-block bg-white text-blue-600 hover:bg-blue-50 px-8 py-3 rounded-lg font-semibold text-lg transition-colors"
          >
            Start Your 14-Day Free Trial
          </Link>
        </div>
      </div>

      {/* Footer */}
      <footer className="bg-gray-900 text-gray-300 py-12">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="mb-6 md:mb-0">
              <div className="flex items-center">
                <Navigation className="text-white mr-2" size={24} />
                <span className="text-xl font-bold text-white">QuickJourney</span>
              </div>
              <p className="mt-2 text-sm">© {new Date().getFullYear()} QuickJourney. All rights reserved.</p>
            </div>
            <div className="flex space-x-6">
              <a href="#" className="hover:text-white transition-colors">Terms</a>
              <a href="#" className="hover:text-white transition-colors">Privacy</a>
              <a href="#" className="hover:text-white transition-colors">Contact</a>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}

import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/components/ui/use-toast';
import { Loader2, Plus, Clock, MapPin, CheckCircle } from 'lucide-react';

export const DashboardPage = () => {
  const { user, signOut } = useAuth();
  const navigate = useNavigate();
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(true);
  const [recentJourneys, setRecentJourneys] = useState([]);
  const [timeSaved, setTimeSaved] = useState(0);
  const [totalJourneys, setTotalJourneys] = useState(0);
  const [isPhoneVerified, setIsPhoneVerified] = useState(false);

  useEffect(() => {
    const loadDashboardData = async () => {
      try {
        setIsLoading(true);
        // TODO: Replace with actual API calls
        // const { data, error } = await fetchDashboardData();
        // if (error) throw error;
        
        // Mock data for now
        setTimeout(() => {
          setRecentJourneys([
            { id: 1, name: 'Morning Deliveries', date: '2023-04-15', stops: 8, timeSaved: 24 },
            { id: 2, name: 'Client Visits', date: '2023-04-14', stops: 5, timeSaved: 18 },
          ]);
          setTimeSaved(42); // Total minutes saved
          setTotalJourneys(2);
          setIsPhoneVerified(user?.phone_verified || false);
          setIsLoading(false);
        }, 800);
      } catch (error) {
        console.error('Error loading dashboard data:', error);
        toast({
          title: 'Error',
          description: 'Failed to load dashboard data. Please try again.',
          variant: 'destructive',
        });
        setIsLoading(false);
      }
    };

    if (user) {
      loadDashboardData();
    } else {
      navigate('/login');
    }
  }, [user, navigate, toast]);

  const handleCreateNewJourney = () => {
    navigate('/journey/new');
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[60vh]">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Welcome back, {user?.user_metadata?.full_name || 'User'}</h1>
          <p className="text-muted-foreground">Here's what's happening with your journeys today.</p>
        </div>
        <Button onClick={handleCreateNewJourney} className="gap-2">
          <Plus className="h-4 w-4" /> New Journey
        </Button>
      </div>

      {!isPhoneVerified && (
        <div className="mb-8 p-4 bg-yellow-50 border-l-4 border-yellow-400 text-yellow-700">
          <div className="flex items-center">
            <CheckCircle className="h-5 w-5 mr-2" />
            <p>Please verify your phone number to unlock all features.</p>
            <Button variant="link" className="ml-auto text-yellow-700 hover:text-yellow-800">
              Verify Now
            </Button>
          </div>
        </div>
      )}

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4 mb-8">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Journeys</CardTitle>
            <MapPin className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalJourneys}</div>
            <p className="text-xs text-muted-foreground">+2 from last month</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Time Saved</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{timeSaved} min</div>
            <p className="text-xs text-muted-foreground">+12% from last month</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Now</CardTitle>
            <div className="h-2 w-2 rounded-full bg-green-500"></div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">1</div>
            <p className="text-xs text-muted-foreground">Currently optimizing</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Upcoming</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">3</div>
            <p className="text-xs text-muted-foreground">Scheduled journeys</p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="recent" className="space-y-4">
        <TabsList>
          <TabsTrigger value="recent">Recent Journeys</TabsTrigger>
          <TabsTrigger value="favorites">Favorites</TabsTrigger>
          <TabsTrigger value="shared">Shared with Me</TabsTrigger>
        </TabsList>
        <TabsContent value="recent" className="space-y-4">
          {recentJourneys.length > 0 ? (
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              {recentJourneys.map((journey) => (
                <Card key={journey.id} className="hover:shadow-md transition-shadow">
                  <CardHeader>
                    <div className="flex justify-between items-start">
                      <div>
                        <CardTitle className="text-lg">{journey.name}</CardTitle>
                        <CardDescription className="mt-1">
                          {new Date(journey.date).toLocaleDateString()}
                        </CardDescription>
                      </div>
                      <Button variant="ghost" size="icon">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="flex justify-between text-sm">
                      <div className="flex items-center">
                        <MapPin className="mr-1 h-3.5 w-3.5 text-muted-foreground" />
                        <span>{journey.stops} stops</span>
                      </div>
                      <div className="flex items-center">
                        <Clock className="mr-1 h-3.5 w-3.5 text-muted-foreground" />
                        <span>Saved {journey.timeSaved} min</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center py-12 border-2 border-dashed rounded-lg">
              <MapPin className="h-12 w-12 text-muted-foreground mb-4" />
              <h3 className="text-lg font-medium mb-1">No journeys yet</h3>
              <p className="text-sm text-muted-foreground mb-4">Create your first journey to get started</p>
              <Button onClick={handleCreateNewJourney} className="gap-2">
                <Plus className="h-4 w-4" /> New Journey
              </Button>
            </div>
          )}
        </TabsContent>
        <TabsContent value="favorites">
          <div className="flex flex-col items-center justify-center py-12 border-2 border-dashed rounded-lg">
            <Star className="h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-medium mb-1">No favorites yet</h3>
            <p className="text-sm text-muted-foreground text-center max-w-md">
              Save your favorite journeys here for quick access
            </p>
          </div>
        </TabsContent>
        <TabsContent value="shared">
          <div className="flex flex-col items-center justify-center py-12 border-2 border-dashed rounded-lg">
            <Users className="h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-medium mb-1">No shared journeys</h3>
            <p className="text-sm text-muted-foreground text-center max-w-md">
              Journeys shared with you will appear here
            </p>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default DashboardPage;

import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { ArrowLeft } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { JourneyForm } from '@/components/journey/JourneyForm';
import { useNotification } from '@/hooks/useNotification';
import { createJourney } from '@/services/journeyService';

export const CreateJourneyPage = () => {
  const navigate = useNavigate();
  const { success, error } = useNotification();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (data: any) => {
    try {
      setIsSubmitting(true);
      await createJourney(data);
      success('Journey Created', 'Your journey has been created successfully!');
      navigate('/app/journeys');
    } catch (err) {
      console.error('Error creating journey:', err);
      error('Error', 'Failed to create journey. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center space-x-4">
        <Button
          variant="outline"
          size="icon"
          onClick={() => navigate(-1)}
          className="shrink-0"
        >
          <ArrowLeft className="h-4 w-4" />
          <span className="sr-only">Back</span>
        </Button>
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Create New Journey</h1>
          <p className="text-muted-foreground">
            Plan your route by adding stops and destinations
          </p>
        </div>
      </div>

      <div className="bg-card rounded-lg border p-6 shadow-sm">
        <JourneyForm onSubmit={handleSubmit} isSubmitting={isSubmitting} />
      </div>
    </div>
  );
};

export default CreateJourneyPage;

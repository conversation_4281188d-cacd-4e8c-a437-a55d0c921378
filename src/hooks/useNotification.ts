import { useCallback } from 'react';
import { useNotifications, NotificationType } from '@/contexts/NotificationContext';

type NotificationOptions = {
  duration?: number;
  action?: {
    label: string;
    onClick: () => void;
  };
};

export const useNotification = () => {
  const { addNotification, removeNotification } = useNotifications();

  const notify = useCallback(
    (type: NotificationType, title: string, message: string, options: NotificationOptions = {}) => {
      const { duration = 5000, action } = options;
      return addNotification({
        type,
        title,
        message,
        duration,
        action,
      });
    },
    [addNotification]
  );

  const success = useCallback(
    (title: string, message: string, options?: NotificationOptions) => {
      return notify('success', title, message, options);
    },
    [notify]
  );

  const error = useCallback(
    (title: string, message: string, options?: NotificationOptions) => {
      return notify('error', title, message, { duration: 10000, ...options });
    },
    [notify]
  );

  const info = useCallback(
    (title: string, message: string, options?: NotificationOptions) => {
      return notify('info', title, message, options);
    },
    [notify]
  );

  const warning = useCallback(
    (title: string, message: string, options?: NotificationOptions) => {
      return notify('warning', title, message, { duration: 8000, ...options });
    },
    [notify]
  );

  return {
    notify,
    success,
    error,
    info,
    warning,
    remove: removeNotification,
  };
};

// Export the notification types for convenience
export type { NotificationType } from '@/contexts/NotificationContext';
